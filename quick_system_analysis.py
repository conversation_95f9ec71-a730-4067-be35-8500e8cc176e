#!/usr/bin/env python3
"""
MANUAL SYSTEM ANALYSIS - PHASE 1
Critical import testing without hanging terminal
"""

import sys
# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): sys.warnoptions = []
from pathlib import Path

# Set up environment
sys.path.insert(0, str(Path(__file__).parent))

print("=" * 80)
print("STARTING - COMPREHENSIVE SYSTEM ANALYSIS - PHASE 1")
print("=" * 80)

print(f"Python Executable: {sys.executable}")
print(f"Python Version: {sys.version}")

# Test critical imports
critical_packages = {
    'pandas': 'Data processing',
    'numpy': 'Numerical computing', 
    'sqlalchemy': 'Database ORM',
    'fastapi': 'Web API framework',
    'uvicorn': 'ASGI server',
    'yaml': 'Configuration parsing',
    'requests': 'HTTP requests',
    'aiohttp': 'Async HTTP',
    'websockets': 'WebSocket support',
    'psutil': 'System monitoring',
    'cryptography': 'Security'
}

print("\nTESTING - TESTING CRITICAL IMPORTS...")
success_count = 0
error_count = 0

for package, description in critical_packages.items():
    try:
        __import__(package)
        print(f"[OK] {package:15} - {description}")
        success_count += 1
    except ImportError as e:
        print(f"[ERROR] {package:15} - {str(e)[:60]}...")
        error_count += 1

print(f"\nDATA - IMPORT RESULTS:")
print(f"   SUCCESS - Successfully imported: {success_count}")
print(f"   ERROR - Failed imports: {error_count}")

# Test database
print(f"\nDATABASE - TESTING DATABASE CONNECTION...")
db_path = Path("E:/bybit_bot_data/bybit_trading_bot_production.db")

if db_path.exists():
    try:
        import sqlite3
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        print(f"[OK] Database connected - {table_count} tables found")
        
        # Test core tables
        core_tables = ['trading_positions', 'trading_orders', 'account_balance', 'bot_config']
        for table in core_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"[OK] Table {table}: {count} records")
            except Exception as e:
                print(f"[ERROR] Table {table}: {e}")
        
        conn.close()
        print("[OK] Database connectivity confirmed")
        
    except Exception as e:
        print(f"[ERROR] Database connection failed: {e}")
else:
    print(f"[ERROR] Database not found at {db_path}")

# Test configuration files
print(f"\nCONFIG - TESTING CONFIGURATION FILES...")
config_files = ['config.yaml', 'main.py', 'main_unified_system.py']

for config_file in config_files:
    if Path(config_file).exists():
        print(f"[OK] {config_file} exists")
    else:
        print(f"[ERROR] {config_file} not found")

# Test core system modules
print(f"\nMODULES - TESTING CORE SYSTEM MODULES...")
core_modules = [
    'bybit_bot.core.config',
    'bybit_bot.core.logger',
    'bybit_bot.database.connection'
]

module_success = 0
module_errors = 0

for module in core_modules:
    try:
        __import__(module)
        print(f"[OK] {module}")
        module_success += 1
    except ImportError as e:
        print(f"[ERROR] {module}: {str(e)[:60]}...")
        module_errors += 1

print(f"\nSUMMARY - PHASE 1 SUMMARY:")
print(f"   Python Environment: {'SUCCESS - OK' if sys.executable.startswith('E:') else 'ERROR - Wrong Path'}")
print(f"   Critical Imports: {success_count}/{len(critical_packages)} successful")
print(f"   Core Modules: {module_success}/{len(core_modules)} loaded")
print(f"   Database: {'SUCCESS - Connected' if db_path.exists() else 'ERROR - Not Found'}")

total_issues = error_count + module_errors + (0 if db_path.exists() else 1)

if total_issues == 0:
    print(f"\nCOMPLETE - PHASE 1 COMPLETE - SYSTEM READY FOR NEXT PHASE")
    print(f"SUCCESS - All critical components operational")
else:
    print(f"\nWARNING - PHASE 1 ISSUES FOUND: {total_issues} critical problems")
    print(f"FIXING - System requires optimization before proceeding")

print(f"\n{'='*80}")
