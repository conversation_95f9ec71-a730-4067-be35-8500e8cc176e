#!/usr/bin/env python3
"""
VALIDATION SCRIPT FOR STREAMLINED MAIN.PY
Quick validation without full system initialization
"""

import sys
import traceback
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_imports():
    """Test basic imports without initialization"""
    print("🔍 Testing basic imports...")
    
    try:
        # Test Python standard library imports
        import asyncio
        import gc
        import logging
        import os
        import sys
        import time
        import tracemalloc
        import weakref
        from contextlib import asynccontextmanager
        from datetime import datetime
        from pathlib import Path
        from typing import Dict, List, Optional, Any
        print("✅ Standard library imports successful")
        
        # Test third-party imports
        import uvicorn
        from fastapi import FastAPI, HTTPException
        from fastapi.middleware.cors import CORSMiddleware
        import psutil
        print("✅ Third-party imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        traceback.print_exc()
        return False

def test_safe_import_function():
    """Test the safe_import function"""
    print("\n🔍 Testing safe_import function...")
    
    try:
        # Import the safe_import function from main
        sys.path.insert(0, str(Path(__file__).parent))
        
        # Test importing the safe_import function
        from main import safe_import
        print("✅ safe_import function imported")
        
        # Test safe import with existing module
        os_module = safe_import('os')
        if os_module:
            print("✅ safe_import works with existing modules")
        
        # Test safe import with non-existing module
        fake_module = safe_import('non_existent_module', 'FakeClass')
        if fake_module is None:
            print("✅ safe_import handles missing modules correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ safe_import test failed: {e}")
        traceback.print_exc()
        return False

def test_memory_optimized_component():
    """Test MemoryOptimizedComponent class"""
    print("\n🔍 Testing MemoryOptimizedComponent...")
    
    try:
        from main import MemoryOptimizedComponent
        
        # Create component
        component = MemoryOptimizedComponent("test_component", critical=True)
        print("✅ MemoryOptimizedComponent created")
        
        # Test attributes
        assert component.name == "test_component"
        assert component.critical == True
        assert component.initialized == False
        print("✅ Component attributes correct")
        
        return True
        
    except Exception as e:
        print(f"❌ MemoryOptimizedComponent test failed: {e}")
        traceback.print_exc()
        return False

def test_websocket_pool_class():
    """Test OptimizedWebSocketPool class structure"""
    print("\n🔍 Testing OptimizedWebSocketPool...")
    
    try:
        from main import OptimizedWebSocketPool
        
        # Create pool
        pool = OptimizedWebSocketPool()
        print("✅ OptimizedWebSocketPool created")
        
        # Test attributes
        assert hasattr(pool, 'connections')
        assert hasattr(pool, 'message_queues')
        assert hasattr(pool, 'connection_locks')
        assert hasattr(pool, 'health_status')
        print("✅ WebSocket pool attributes correct")
        
        return True
        
    except Exception as e:
        print(f"❌ OptimizedWebSocketPool test failed: {e}")
        traceback.print_exc()
        return False

def test_streamlined_system_class():
    """Test StreamlinedTradingSystem class structure"""
    print("\n🔍 Testing StreamlinedTradingSystem...")
    
    try:
        from main import StreamlinedTradingSystem
        
        # Create system (without initialization)
        system = StreamlinedTradingSystem()
        print("✅ StreamlinedTradingSystem created")
        
        # Test basic attributes
        assert hasattr(system, 'config')
        assert hasattr(system, 'logger')
        assert hasattr(system, 'components')
        assert hasattr(system, 'health_metrics')
        assert hasattr(system, 'ws_pool')
        print("✅ System attributes correct")
        
        # Test health status method
        health = system.get_health_status()
        assert 'status' in health
        assert 'metrics' in health
        assert 'components' in health
        print("✅ Health status method works")
        
        return True
        
    except Exception as e:
        print(f"❌ StreamlinedTradingSystem test failed: {e}")
        traceback.print_exc()
        return False

def test_fastapi_app():
    """Test FastAPI app creation"""
    print("\n🔍 Testing FastAPI app...")
    
    try:
        from main import app
        
        # Test app exists
        assert app is not None
        print("✅ FastAPI app created")
        
        # Test app configuration
        assert app.title == "Autonomous Bybit Trading Bot"
        assert app.version == "2.0.0"
        print("✅ App configuration correct")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app test failed: {e}")
        traceback.print_exc()
        return False

def test_critical_imports():
    """Test critical component imports"""
    print("\n🔍 Testing critical component imports...")
    
    try:
        from main import BotConfig, TradingBotLogger, DatabaseManager
        print("✅ Core components imported")
        
        from main import EnhancedBybitClient
        print("✅ Enhanced Bybit client imported")
        
        from main import AdvancedProfitEngine, HyperProfitEngine
        print("✅ Profit engines imported")
        
        from main import AdvancedRiskManager
        print("✅ Risk manager imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Critical imports test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main validation function"""
    print("🚀 STREAMLINED MAIN.PY VALIDATION")
    print("=" * 80)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Safe Import Function", test_safe_import_function),
        ("Memory Optimized Component", test_memory_optimized_component),
        ("WebSocket Pool Class", test_websocket_pool_class),
        ("Streamlined System Class", test_streamlined_system_class),
        ("FastAPI App", test_fastapi_app),
        ("Critical Component Imports", test_critical_imports)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 VALIDATION RESULTS")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 VALIDATION SUCCESSFUL - MAIN.PY IS READY")
        print("🚀 System can be deployed for live trading")
        return True
    else:
        print("⚠️ VALIDATION ISSUES FOUND - REVIEW BEFORE DEPLOYMENT")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Validation crashed: {e}")
        traceback.print_exc()
        sys.exit(1)
